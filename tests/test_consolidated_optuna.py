"""
Объединенные тесты Optuna оптимизации.
Консолидирует ключевые тесты из нескольких файлов в параметризованные тесты.
"""

import pytest
import optuna
import tempfile
import os
from unittest.mock import Mock, patch

from src.optimiser.fast_objective import FastWalkForwardObjective


class TestConsolidatedOptuna:
    """Объединенные тесты для Optuna оптимизации."""
    
    def setup_method(self):
        """Настройка тестового окружения."""
        # Используем моки вместо реальных файлов для изоляции unit-тестов
        self.base_config_path = "configs/main_2024.yaml"
        self.search_space_path = "configs/search_space_fast.yaml"
    
    @pytest.mark.slow
    @pytest.mark.serial
    @pytest.mark.parametrize("test_scenario", [
        "parameter_generation_without_log",
        "objective_initialization"
    ])
    def test_optuna_objective_functionality_when_scenario_tested_then_works_correctly(self, test_scenario, fast_study):
        """Параметризованный тест для функциональности Optuna objective."""

        if test_scenario == "parameter_generation_without_log":
            self._test_parameter_generation_without_log(fast_study)
        elif test_scenario == "objective_initialization":
            self._test_objective_initialization(fast_study)
    
    def _test_objective_initialization(self, study):
        """Тест инициализации objective."""
        objective = FastWalkForwardObjective(self.base_config_path, self.search_space_path)

        # Проверяем базовые атрибуты
        assert hasattr(objective, 'base_config')
        assert hasattr(objective, 'search_space')
        assert hasattr(objective, '__call__')
        assert callable(objective)
    
    def _test_parameter_generation_without_log(self, study):
        """Тест генерации параметров без логирования."""
        trial = study.ask()

        # Проверяем, что параметры генерируются
        rolling_window = trial.suggest_int('rolling_window', 10, 50)
        z_threshold = trial.suggest_float('z_threshold', 1.0, 3.0)

        assert 10 <= rolling_window <= 50
        assert 1.0 <= z_threshold <= 3.0
    
    @pytest.mark.slow
    @pytest.mark.serial
    @pytest.mark.parametrize("integration_scenario", [
        "parameter_types_consistency",
        "normalization_config_section",
        "all_fixes_integration"
    ])
    def test_optuna_integration_when_scenario_tested_then_parameters_consistent(self, integration_scenario, fast_study):
        """Параметризованный тест для интеграции Optuna."""

        if integration_scenario == "parameter_types_consistency":
            self._test_parameter_types_consistency(fast_study)
        elif integration_scenario == "normalization_config_section":
            self._test_normalization_config_section(fast_study)
        elif integration_scenario == "all_fixes_integration":
            self._test_all_fixes_integration(fast_study)
    
    def _test_parameter_types_consistency(self, study):
        """Тест консистентности типов параметров."""
        trial = study.ask()
        
        # Проверяем различные типы параметров
        int_param = trial.suggest_int('test_int', 1, 10, step=2)
        float_param = trial.suggest_float('test_float', 0.1, 1.0)
        categorical_param = trial.suggest_categorical('test_cat', ['a', 'b', 'c'])
        
        assert isinstance(int_param, int)
        assert isinstance(float_param, float)
        assert categorical_param in ['a', 'b', 'c']
    
    def _test_normalization_config_section(self, study):
        """Тест секции конфигурации нормализации."""
        # Используем стандартную конфигурацию, которая уже содержит нормализацию
        objective = FastWalkForwardObjective(self.base_config_path, self.search_space_path)

        # Проверяем, что objective может быть создан без ошибок
        assert hasattr(objective, '__call__')
        assert callable(objective)

        # Проверяем, что конфигурация загружена корректно
        assert hasattr(objective, 'base_config'), "Должна быть загружена базовая конфигурация"
        assert hasattr(objective, 'search_space'), "Должно быть загружено пространство поиска"
        assert objective.base_config is not None, "Базовая конфигурация не должна быть None"

        # Проверяем, что в search_space нет 'filters' (что было основной проблемой)
        assert 'filters' not in objective.search_space, \
            "В fast-режиме 'filters' не должны присутствовать в search_space"
    
    def _test_all_fixes_integration(self, study):
        """Тест интеграции всех исправлений."""
        objective = FastWalkForwardObjective(self.base_config_path, self.search_space_path)
        trial = study.ask()

        # Проверяем базовую функциональность
        assert hasattr(objective, '__call__')
        assert callable(objective)
        
        # Проверяем, что можно создать параметры
        params = {
            'rolling_window': trial.suggest_int('rolling_window', 20, 40),
            'z_threshold': trial.suggest_float('z_threshold', 1.5, 2.5),
            'z_exit': trial.suggest_float('z_exit', 0.3, 0.7)
        }
        
        for key, value in params.items():
            assert isinstance(value, (int, float))
    
    @pytest.mark.slow
    @pytest.mark.serial
    def test_critical_fixes_when_validated_comprehensively_then_work_correctly(self, fast_study):
        """Комплексный тест критических исправлений."""
        objective = FastWalkForwardObjective(self.base_config_path, self.search_space_path)

        # Проверяем, что objective создается без ошибок
        assert hasattr(objective, 'base_config')
        assert hasattr(objective, 'search_space')

        # Проверяем, что можем создать trial
        trial = fast_study.ask()
        assert trial is not None

        # Проверяем базовую функциональность параметров
        param = trial.suggest_float('test_param', 0.1, 1.0)
        assert 0.1 <= param <= 1.0
    
    @pytest.mark.slow
    @pytest.mark.serial
    def test_sqlite_concurrency_when_concurrent_access_then_handled_correctly(self, tmp_path):
        """Тест обработки конкурентности SQLite."""
        # Создаем временную базу данных
        db_path = tmp_path / "test_optuna.db"
        storage_url = f"sqlite:///{db_path}"

        study = optuna.create_study(
            storage=storage_url,
            study_name="test_concurrency",
            load_if_exists=True,
            direction="minimize"
        )

        # Тестируем создание trial и базовые операции
        trial = study.ask()
        param = trial.suggest_float('test_param', 0.1, 1.0)
        study.tell(trial, param)  # Используем простое значение

        assert len(study.trials) == 1
        assert study.trials[0].value == param
